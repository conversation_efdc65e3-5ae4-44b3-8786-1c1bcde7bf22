const STORAGE_KEYS = {
  WORKSPACES: "workspaces",
  SETTINGS: "settings",
  ACTIVE_WORKSPACE_ID: "activeWorkspaceId",
  LAST_ACTIVE_WORKSPACE_IDS: "lastActiveWorkspaceIds"
};
const DEFAULT_SETTINGS = {
  autoCloseOtherTabs: true,
  preserveUserOpenedTabs: false,
  defaultWorkspaceOnStartup: "",
  sidebarWidth: 320,
  theme: "dark",
  showFavicons: true,
  confirmBeforeDelete: true,
  maxRecentWorkspaces: 5
};
const WORKSPACE_COLORS = [
  "#3b82f6",
  // blue
  "#10b981",
  // emerald
  "#f59e0b",
  // amber
  "#ef4444",
  // red
  "#8b5cf6",
  // violet
  "#06b6d4",
  // cyan
  "#84cc16",
  // lime
  "#f97316",
  // orange
  "#ec4899",
  // pink
  "#6366f1"
  // indigo
];
const WORKSPACE_ICONS = [
  "🚀",
  "💼",
  "🔬",
  "🎨",
  "📊",
  "🛠️",
  "📚",
  "💡",
  "🎯",
  "⚡",
  "🌟",
  "🔥",
  "💎",
  "🎪",
  "🎭",
  "🎨",
  "🎵",
  "🎮",
  "🏆",
  "🎊",
  "📱",
  "💻",
  "🖥️",
  "⌨️",
  "🖱️",
  "🖨️",
  "📷",
  "📹",
  "🎥",
  "📺",
  "🔍",
  "🔎",
  "🔬",
  "🔭",
  "📡",
  "🛰️",
  "🚁",
  "✈️",
  "🚀",
  "🛸"
];
const WORKSPACE_TEMPLATES = [
  {
    id: "ai-tools",
    name: "AI工具集",
    description: "常用的AI工具和平台",
    icon: "🤖",
    color: "#3b82f6",
    category: "ai-tools",
    websites: [
      {
        url: "https://chat.openai.com",
        title: "ChatGPT",
        favicon: "https://chat.openai.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://claude.ai",
        title: "Claude",
        favicon: "https://claude.ai/favicon.ico",
        isPinned: true
      },
      {
        url: "https://gemini.google.com",
        title: "Gemini",
        favicon: "https://gemini.google.com/favicon.ico",
        isPinned: true
      }
    ]
  },
  {
    id: "development",
    name: "开发环境",
    description: "编程开发相关工具",
    icon: "💻",
    color: "#10b981",
    category: "development",
    websites: [
      {
        url: "https://github.com",
        title: "GitHub",
        favicon: "https://github.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://stackoverflow.com",
        title: "Stack Overflow",
        favicon: "https://stackoverflow.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://developer.mozilla.org",
        title: "MDN Web Docs",
        favicon: "https://developer.mozilla.org/favicon.ico",
        isPinned: true
      }
    ]
  },
  {
    id: "design",
    name: "设计工具",
    description: "设计和创意工具",
    icon: "🎨",
    color: "#f59e0b",
    category: "design",
    websites: [
      {
        url: "https://www.figma.com",
        title: "Figma",
        favicon: "https://www.figma.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://dribbble.com",
        title: "Dribbble",
        favicon: "https://dribbble.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://www.behance.net",
        title: "Behance",
        favicon: "https://www.behance.net/favicon.ico",
        isPinned: true
      }
    ]
  },
  {
    id: "research",
    name: "学术研究",
    description: "学术研究和论文查找",
    icon: "🔬",
    color: "#8b5cf6",
    category: "research",
    websites: [
      {
        url: "https://arxiv.org",
        title: "arXiv",
        favicon: "https://arxiv.org/favicon.ico",
        isPinned: true
      },
      {
        url: "https://scholar.google.com",
        title: "Google Scholar",
        favicon: "https://scholar.google.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://www.researchgate.net",
        title: "ResearchGate",
        favicon: "https://www.researchgate.net/favicon.ico",
        isPinned: true
      }
    ]
  },
  {
    id: "productivity",
    name: "生产力工具",
    description: "提高工作效率的工具",
    icon: "⚡",
    color: "#06b6d4",
    category: "productivity",
    websites: [
      {
        url: "https://notion.so",
        title: "Notion",
        favicon: "https://notion.so/favicon.ico",
        isPinned: true
      },
      {
        url: "https://trello.com",
        title: "Trello",
        favicon: "https://trello.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://calendar.google.com",
        title: "Google Calendar",
        favicon: "https://calendar.google.com/favicon.ico",
        isPinned: true
      }
    ]
  }
];
const ERROR_CODES = {
  WORKSPACE_NOT_FOUND: "WORKSPACE_NOT_FOUND",
  WEBSITE_NOT_FOUND: "WEBSITE_NOT_FOUND",
  STORAGE_ERROR: "STORAGE_ERROR",
  TAB_ERROR: "TAB_ERROR",
  WINDOW_ERROR: "WINDOW_ERROR",
  PERMISSION_DENIED: "PERMISSION_DENIED",
  INVALID_URL: "INVALID_URL",
  DUPLICATE_WORKSPACE: "DUPLICATE_WORKSPACE",
  DUPLICATE_WEBSITE: "DUPLICATE_WEBSITE"
};
const COMMANDS = {
  SWITCH_WORKSPACE_1: "switch-workspace-1",
  SWITCH_WORKSPACE_2: "switch-workspace-2",
  SWITCH_WORKSPACE_3: "switch-workspace-3",
  TOGGLE_SIDEPANEL: "toggle-sidepanel"
};
const URL_REGEX = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
const DEFAULT_FAVICON = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="m9 12 2 2 4-4"/></svg>';

class StorageManager {
  /**
   * 获取所有存储数据
   */
  static async getAllData() {
    try {
      const result = await chrome.storage.local.get([
        STORAGE_KEYS.WORKSPACES,
        STORAGE_KEYS.SETTINGS,
        STORAGE_KEYS.ACTIVE_WORKSPACE_ID,
        STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS
      ]);
      const data = {
        workspaces: result[STORAGE_KEYS.WORKSPACES] || [],
        settings: { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] },
        activeWorkspaceId: result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null,
        lastActiveWorkspaceIds: result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || []
      };
      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get storage data",
          details: error
        }
      };
    }
  }
  /**
   * 获取所有工作区
   */
  static async getWorkspaces() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.WORKSPACES);
      const workspaces = result[STORAGE_KEYS.WORKSPACES] || [];
      workspaces.sort((a, b) => a.order - b.order);
      return { success: true, data: workspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 保存工作区列表
   */
  static async saveWorkspaces(workspaces) {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.WORKSPACES]: workspaces
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 获取单个工作区
   */
  static async getWorkspace(id) {
    const result = await this.getWorkspaces();
    if (!result.success) {
      return {
        success: false,
        error: result.error
      };
    }
    const workspace = result.data.find((w) => w.id === id);
    if (!workspace) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_NOT_FOUND,
          message: `Workspace with id ${id} not found`
        }
      };
    }
    return { success: true, data: workspace };
  }
  /**
   * 获取设置
   */
  static async getSettings() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.SETTINGS);
      const settings = { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] };
      return { success: true, data: settings };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get settings",
          details: error
        }
      };
    }
  }
  /**
   * 保存设置
   */
  static async saveSettings(settings) {
    try {
      const currentResult = await this.getSettings();
      if (!currentResult.success) {
        return {
          success: false,
          error: currentResult.error
        };
      }
      const updatedSettings = { ...currentResult.data, ...settings };
      await chrome.storage.local.set({
        [STORAGE_KEYS.SETTINGS]: updatedSettings
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save settings",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前活跃工作区ID
   */
  static async getActiveWorkspaceId() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.ACTIVE_WORKSPACE_ID);
      return { success: true, data: result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get active workspace ID",
          details: error
        }
      };
    }
  }
  /**
   * 设置当前活跃工作区ID
   */
  static async setActiveWorkspaceId(id) {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.ACTIVE_WORKSPACE_ID]: id
      });
      if (id) {
        await this.updateLastActiveWorkspaces(id);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to set active workspace ID",
          details: error
        }
      };
    }
  }
  /**
   * 更新最近使用的工作区列表
   */
  static async updateLastActiveWorkspaces(workspaceId) {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS);
      let lastActiveIds = result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [];
      lastActiveIds = lastActiveIds.filter((id) => id !== workspaceId);
      lastActiveIds.unshift(workspaceId);
      const settingsResult = await this.getSettings();
      const maxRecent = settingsResult.success ? settingsResult.data.maxRecentWorkspaces : 5;
      lastActiveIds = lastActiveIds.slice(0, maxRecent);
      await chrome.storage.local.set({
        [STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS]: lastActiveIds
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update last active workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 清除所有数据
   */
  static async clearAll() {
    try {
      await chrome.storage.local.clear();
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to clear storage",
          details: error
        }
      };
    }
  }
  /**
   * 监听存储变化
   */
  static onChanged(callback) {
    chrome.storage.onChanged.addListener((changes, areaName) => {
      if (areaName === "local") {
        callback(changes);
      }
    });
  }
  /**
   * 导出数据
   */
  static async exportData() {
    try {
      const dataResult = await this.getAllData();
      if (!dataResult.success) {
        return {
          success: false,
          error: dataResult.error
        };
      }
      const exportData = {
        version: "1.0.0",
        exportedAt: Date.now(),
        workspaces: dataResult.data.workspaces,
        settings: dataResult.data.settings
      };
      return { success: true, data: JSON.stringify(exportData, null, 2) };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to export data",
          details: error
        }
      };
    }
  }
  /**
   * 导入数据
   */
  static async importData(jsonData) {
    try {
      const importData = JSON.parse(jsonData);
      if (importData.workspaces) {
        await this.saveWorkspaces(importData.workspaces);
      }
      if (importData.settings) {
        await this.saveSettings(importData.settings);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to import data",
          details: error
        }
      };
    }
  }
}

class WindowManager {
  static workspaceWindows = /* @__PURE__ */ new Map();
  // workspaceId -> windowId
  static windowWorkspaces = /* @__PURE__ */ new Map();
  // windowId -> workspaceId
  /**
   * 为工作区创建专用窗口
   */
  static async createWorkspaceWindow(workspaceId, workspaceName) {
    try {
      console.log(`创建工作区专用窗口: ${workspaceName} (${workspaceId})`);
      const existingWindowId = this.workspaceWindows.get(workspaceId);
      if (existingWindowId) {
        try {
          const window2 = await chrome.windows.get(existingWindowId);
          if (window2) {
            console.log(`工作区 ${workspaceName} 的专用窗口已存在: ${existingWindowId}`);
            return {
              success: true,
              data: {
                id: existingWindowId,
                workspaceId,
                workspaceName,
                tabCount: window2.tabs?.length || 0,
                isVisible: window2.state !== "minimized"
              }
            };
          }
        } catch {
          this.workspaceWindows.delete(workspaceId);
          this.windowWorkspaces.delete(existingWindowId);
        }
      }
      const window = await chrome.windows.create({
        type: "normal",
        state: "normal",
        focused: false,
        // 不获取焦点
        width: 1200,
        height: 800,
        left: 100,
        top: 100,
        url: chrome.runtime.getURL("workspace-placeholder.html") + `?workspaceId=${workspaceId}&workspaceName=${encodeURIComponent(workspaceName)}`
      });
      if (!window.id) {
        throw new Error("Failed to create window");
      }
      this.workspaceWindows.set(workspaceId, window.id);
      this.windowWorkspaces.set(window.id, workspaceId);
      console.log(`成功创建工作区专用窗口: ${workspaceName} -> 窗口ID ${window.id}`);
      return {
        success: true,
        data: {
          id: window.id,
          workspaceId,
          workspaceName,
          tabCount: window.tabs?.length || 1,
          isVisible: true
        }
      };
    } catch (error) {
      console.error(`创建工作区专用窗口失败: ${workspaceName}`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to create workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区的专用窗口ID
   */
  static getWorkspaceWindowId(workspaceId) {
    return this.workspaceWindows.get(workspaceId);
  }
  /**
   * 获取窗口对应的工作区ID
   */
  static getWindowWorkspaceId(windowId) {
    return this.windowWorkspaces.get(windowId);
  }
  /**
   * 将标签页移动到工作区专用窗口
   */
  static async moveTabsToWorkspaceWindow(tabIds, workspaceId, workspaceName) {
    try {
      if (tabIds.length === 0) {
        return { success: true };
      }
      console.log(`移动 ${tabIds.length} 个标签页到工作区 ${workspaceName}`);
      const windowResult = await this.createWorkspaceWindow(workspaceId, workspaceName);
      if (!windowResult.success) {
        return windowResult;
      }
      const windowId = windowResult.data.id;
      await chrome.tabs.move(tabIds, {
        windowId,
        index: -1
        // 移动到窗口末尾
      });
      console.log(`成功移动 ${tabIds.length} 个标签页到工作区专用窗口 ${windowId}`);
      return { success: true };
    } catch (error) {
      console.error(`移动标签页到工作区专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs to workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 将标签页从工作区专用窗口移动到主窗口
   */
  static async moveTabsFromWorkspaceWindow(workspaceId, targetWindowId) {
    try {
      const windowId = this.workspaceWindows.get(workspaceId);
      if (!windowId) {
        console.log(`工作区 ${workspaceId} 没有专用窗口`);
        return { success: true, data: [] };
      }
      const tabs = await chrome.tabs.query({ windowId });
      const workspaceTabs = tabs.filter(
        (tab) => !tab.url?.includes("workspace-placeholder.html")
      );
      if (workspaceTabs.length === 0) {
        console.log(`工作区专用窗口 ${windowId} 中没有需要移动的标签页`);
        return { success: true, data: [] };
      }
      let targetWindow = targetWindowId;
      if (!targetWindow) {
        const currentWindow = await chrome.windows.getCurrent();
        targetWindow = currentWindow.id;
      }
      console.log(`从工作区专用窗口 ${windowId} 移动 ${workspaceTabs.length} 个标签页到窗口 ${targetWindow}`);
      const tabIds = workspaceTabs.map((tab) => tab.id);
      await chrome.tabs.move(tabIds, {
        windowId: targetWindow,
        index: -1
      });
      const tabInfos = workspaceTabs.map((tab) => ({
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: targetWindow
      }));
      console.log(`成功移动 ${workspaceTabs.length} 个标签页到主窗口`);
      return { success: true, data: tabInfos };
    } catch (error) {
      console.error(`从工作区专用窗口移动标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs from workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 关闭工作区专用窗口
   */
  static async closeWorkspaceWindow(workspaceId) {
    try {
      const windowId = this.workspaceWindows.get(workspaceId);
      if (!windowId) {
        console.log(`工作区 ${workspaceId} 没有专用窗口需要关闭`);
        return { success: true };
      }
      console.log(`关闭工作区专用窗口: ${windowId}`);
      await this.moveTabsFromWorkspaceWindow(workspaceId);
      await chrome.windows.remove(windowId);
      this.workspaceWindows.delete(workspaceId);
      this.windowWorkspaces.delete(windowId);
      console.log(`成功关闭工作区专用窗口: ${windowId}`);
      return { success: true };
    } catch (error) {
      console.error(`关闭工作区专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to close workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 获取所有工作区专用窗口信息
   */
  static async getAllWorkspaceWindows() {
    try {
      const windowInfos = [];
      for (const [workspaceId, windowId] of this.workspaceWindows.entries()) {
        try {
          const window = await chrome.windows.get(windowId, { populate: true });
          const workspaceName = this.windowWorkspaces.get(windowId) || "Unknown";
          windowInfos.push({
            id: windowId,
            workspaceId,
            workspaceName,
            tabCount: window.tabs?.length || 0,
            isVisible: window.state !== "minimized"
          });
        } catch {
          this.workspaceWindows.delete(workspaceId);
          this.windowWorkspaces.delete(windowId);
        }
      }
      return { success: true, data: windowInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to get workspace windows",
          details: error
        }
      };
    }
  }
  /**
   * 更新窗口标题
   */
  static async updateWindowTitle(workspaceId, workspaceName, tabCount) {
    try {
      const windowId = this.workspaceWindows.get(workspaceId);
      if (!windowId) {
        return { success: true };
      }
      const tabs = await chrome.tabs.query({ windowId });
      const placeholderTab = tabs.find(
        (tab) => tab.url?.includes("workspace-placeholder.html")
      );
      if (placeholderTab) {
        const newUrl = chrome.runtime.getURL("workspace-placeholder.html") + `?workspaceId=${workspaceId}&workspaceName=${encodeURIComponent(workspaceName)}&tabCount=${tabCount}`;
        await chrome.tabs.update(placeholderTab.id, { url: newUrl });
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to update window title",
          details: error
        }
      };
    }
  }
}

class WorkspaceManager {
  /**
   * 生成唯一ID
   */
  static generateId() {
    return `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**
   * 生成网站ID
   */
  static generateWebsiteId() {
    return `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**
   * 验证URL格式
   */
  static isValidUrl(url) {
    return URL_REGEX.test(url);
  }
  /**
   * 获取网站favicon
   */
  static async getFavicon(url) {
    try {
      const domain = new URL(url).origin;
      return `${domain}/favicon.ico`;
    } catch {
      return DEFAULT_FAVICON;
    }
  }
  /**
   * 获取网站标题
   */
  static async getWebsiteTitle(url) {
    try {
      const tabs = await chrome.tabs.query({ url });
      if (tabs.length > 0 && tabs[0].title) {
        return tabs[0].title;
      }
      const domain = new URL(url).hostname;
      return domain.replace("www.", "");
    } catch {
      return url;
    }
  }
  /**
   * 创建新工作区
   */
  static async createWorkspace(options) {
    try {
      if (!options.name.trim()) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: "Workspace name cannot be empty"
          }
        };
      }
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error
        };
      }
      const existingWorkspaces = workspacesResult.data;
      if (existingWorkspaces.some((w) => w.name === options.name)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WORKSPACE,
            message: "Workspace with this name already exists"
          }
        };
      }
      const workspace = {
        id: this.generateId(),
        name: options.name,
        icon: options.icon || WORKSPACE_ICONS[Math.floor(Math.random() * WORKSPACE_ICONS.length)],
        color: options.color || WORKSPACE_COLORS[Math.floor(Math.random() * WORKSPACE_COLORS.length)],
        websites: [],
        createdAt: Date.now(),
        updatedAt: Date.now(),
        isActive: false,
        order: existingWorkspaces.length
      };
      if (options.websites) {
        for (let i = 0; i < options.websites.length; i++) {
          const siteData = options.websites[i];
          const website = {
            id: this.generateWebsiteId(),
            url: siteData.url,
            title: siteData.title || await this.getWebsiteTitle(siteData.url),
            favicon: siteData.favicon || await this.getFavicon(siteData.url),
            isPinned: siteData.isPinned,
            addedAt: Date.now(),
            order: i
          };
          workspace.websites.push(website);
        }
      }
      existingWorkspaces.push(workspace);
      const saveResult = await StorageManager.saveWorkspaces(existingWorkspaces);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error
        };
      }
      if (options.activate) {
        await StorageManager.setActiveWorkspaceId(workspace.id);
        workspace.isActive = true;
      }
      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to create workspace",
          details: error
        }
      };
    }
  }
  /**
   * 更新工作区
   */
  static async updateWorkspace(id, options) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;
      const workspaces = workspacesResult.data;
      const workspaceIndex = workspaces.findIndex((w) => w.id === id);
      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${id} not found`
          }
        };
      }
      const workspace = workspaces[workspaceIndex];
      if (options.name !== void 0) workspace.name = options.name;
      if (options.icon !== void 0) workspace.icon = options.icon;
      if (options.color !== void 0) workspace.color = options.color;
      if (options.websites !== void 0) workspace.websites = options.websites;
      if (options.isActive !== void 0) workspace.isActive = options.isActive;
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update workspace",
          details: error
        }
      };
    }
  }
  /**
   * 删除工作区
   */
  static async deleteWorkspace(id) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;
      const workspaces = workspacesResult.data;
      const workspaceIndex = workspaces.findIndex((w) => w.id === id);
      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${id} not found`
          }
        };
      }
      console.log(`删除工作区 ${id}，关闭专用窗口`);
      const closeWindowResult = await WindowManager.closeWorkspaceWindow(id);
      if (!closeWindowResult.success) {
        console.error(`关闭工作区专用窗口失败:`, closeWindowResult.error);
      }
      workspaces.splice(workspaceIndex, 1);
      workspaces.forEach((workspace, index) => {
        workspace.order = index;
      });
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data === id) {
        await StorageManager.setActiveWorkspaceId(null);
      }
      console.log(`成功删除工作区: ${id}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to delete workspace",
          details: error
        }
      };
    }
  }
  /**
   * 添加网站到工作区
   */
  static async addWebsite(workspaceId, url, options = {}) {
    try {
      if (!this.isValidUrl(url)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: "Invalid URL format"
          }
        };
      }
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      if (workspace.websites.some((w) => w.url === url)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WEBSITE,
            message: "Website with this URL already exists in workspace"
          }
        };
      }
      const website = {
        id: this.generateWebsiteId(),
        url,
        title: options.title || await this.getWebsiteTitle(url),
        favicon: options.favicon || await this.getFavicon(url),
        isPinned: options.pinTab || false,
        addedAt: Date.now(),
        order: workspace.websites.length
      };
      workspace.websites.push(website);
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      if (options.openInNewTab) {
        await chrome.tabs.create({
          url,
          pinned: options.pinTab || false
        });
      }
      return { success: true, data: website };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to add website",
          details: error
        }
      };
    }
  }
  /**
   * 从工作区移除网站
   */
  static async removeWebsite(workspaceId, websiteId) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      const websiteIndex = workspace.websites.findIndex((w) => w.id === websiteId);
      if (websiteIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: `Website with id ${websiteId} not found`
          }
        };
      }
      workspace.websites.splice(websiteIndex, 1);
      workspace.websites.forEach((website, index) => {
        website.order = index;
      });
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to remove website",
          details: error
        }
      };
    }
  }
  /**
   * 更新工作区中的网站
   */
  static async updateWebsite(workspaceId, websiteId, updates) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error
        };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      const website = workspace.websites.find((w) => w.id === websiteId);
      if (!website) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: `Website with id ${websiteId} not found`
          }
        };
      }
      if (updates.url !== void 0) {
        if (!this.isValidUrl(updates.url)) {
          return {
            success: false,
            error: {
              code: ERROR_CODES.INVALID_URL,
              message: "Invalid URL format"
            }
          };
        }
        website.url = updates.url;
        website.favicon = await this.getFavicon(updates.url);
      }
      if (updates.title !== void 0) {
        website.title = updates.title;
      }
      if (updates.isPinned !== void 0) {
        website.isPinned = updates.isPinned;
      }
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error
        };
      }
      return { success: true, data: website };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update website",
          details: error
        }
      };
    }
  }
  /**
   * 重新排序工作区
   */
  static async reorderWorkspaces(workspaceIds) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;
      const workspaces = workspacesResult.data;
      const reorderedWorkspaces = [];
      workspaceIds.forEach((id, index) => {
        const workspace = workspaces.find((w) => w.id === id);
        if (workspace) {
          workspace.order = index;
          reorderedWorkspaces.push(workspace);
        }
      });
      const saveResult = await StorageManager.saveWorkspaces(reorderedWorkspaces);
      if (!saveResult.success) return saveResult;
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to reorder workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 重新排序工作区内的网站
   */
  static async reorderWebsites(workspaceId, websiteIds) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      const reorderedWebsites = [];
      websiteIds.forEach((id, index) => {
        const website = workspace.websites.find((w) => w.id === id);
        if (website) {
          website.order = index;
          reorderedWebsites.push(website);
        }
      });
      workspace.websites = reorderedWebsites;
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to reorder websites",
          details: error
        }
      };
    }
  }
}

class TabManager {
  /**
   * 获取所有标签页信息
   */
  static async getAllTabs() {
    try {
      const tabs = await chrome.tabs.query({});
      const tabInfos = tabs.map((tab) => ({
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId
      }));
      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前活跃标签页
   */
  static async getActiveTab() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "No active tab found"
          }
        };
      }
      const tab = tabs[0];
      const tabInfo = {
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get active tab",
          details: error
        }
      };
    }
  }
  /**
   * 检查URL是否已在标签页中打开
   */
  static async findTabByUrl(url) {
    try {
      let tabs = await chrome.tabs.query({ url });
      if (tabs.length === 0) {
        try {
          const targetDomain = new URL(url).hostname;
          const allTabs = await chrome.tabs.query({});
          tabs = allTabs.filter((tab2) => {
            if (!tab2.url) return false;
            try {
              const tabDomain = new URL(tab2.url).hostname;
              return tabDomain === targetDomain;
            } catch {
              return false;
            }
          });
        } catch {
          return { success: true, data: null };
        }
      }
      if (tabs.length === 0) {
        return { success: true, data: null };
      }
      const tab = tabs[0];
      const tabInfo = {
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to find tab by URL",
          details: error
        }
      };
    }
  }
  /**
   * 创建新标签页
   */
  static async createTab(url, pinned = false, active = true) {
    try {
      const tab = await chrome.tabs.create({
        url,
        pinned,
        active
      });
      const tabInfo = {
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to create tab",
          details: error
        }
      };
    }
  }
  /**
   * 激活标签页
   */
  static async activateTab(tabId) {
    try {
      console.log(`Activating tab ${tabId}`);
      await chrome.tabs.update(tabId, { active: true });
      console.log(`Successfully activated tab ${tabId}`);
      return { success: true };
    } catch (error) {
      console.error(`Failed to activate tab ${tabId}:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to activate tab",
          details: error
        }
      };
    }
  }
  /**
   * 固定/取消固定标签页
   */
  static async pinTab(tabId, pinned) {
    try {
      console.log(`${pinned ? "Pinning" : "Unpinning"} tab ${tabId}`);
      await chrome.tabs.update(tabId, { pinned });
      console.log(`Successfully ${pinned ? "pinned" : "unpinned"} tab ${tabId}`);
      return { success: true };
    } catch (error) {
      console.error(`Failed to ${pinned ? "pin" : "unpin"} tab ${tabId}:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to pin/unpin tab",
          details: error
        }
      };
    }
  }
  /**
   * 关闭标签页
   */
  static async closeTab(tabId) {
    try {
      await chrome.tabs.remove(tabId);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to close tab",
          details: error
        }
      };
    }
  }
  /**
   * 关闭多个标签页
   */
  static async closeTabs(tabIds) {
    try {
      await chrome.tabs.remove(tabIds);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to close tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区相关的标签页
   */
  static async getWorkspaceRelatedTabs(workspace) {
    try {
      const allTabsResult = await this.getAllTabs();
      if (!allTabsResult.success) return allTabsResult;
      const allTabs = allTabsResult.data;
      const workspaceUrls = workspace.websites.map((w) => w.url);
      const relatedTabs = allTabs.filter(
        (tab) => workspaceUrls.some((url) => tab.url.startsWith(url))
      );
      return { success: true, data: relatedTabs };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace related tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取非工作区相关的标签页
   */
  static async getNonWorkspaceRelatedTabs(workspace) {
    try {
      const allTabsResult = await this.getAllTabs();
      if (!allTabsResult.success) return allTabsResult;
      const allTabs = allTabsResult.data;
      const workspaceUrls = workspace.websites.map((w) => w.url);
      const nonRelatedTabs = allTabs.filter(
        (tab) => !workspaceUrls.some((url) => tab.url.startsWith(url))
      );
      return { success: true, data: nonRelatedTabs };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get non-workspace related tabs",
          details: error
        }
      };
    }
  }
  /**
   * 检查标签页是否为用户手动打开的
   */
  static async isUserOpenedTab(tabId) {
    try {
      const tab = await chrome.tabs.get(tabId);
      return !tab.pinned;
    } catch {
      return false;
    }
  }
}

class WorkspaceSwitcher {
  /**
   * 切换到指定工作区（专用窗口架构）
   */
  static async switchToWorkspace(workspaceId, options = {}) {
    try {
      console.log(`开始切换到工作区: ${workspaceId}`);
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) return workspaceResult;
      const workspace = workspaceResult.data;
      const currentWorkspaceResult = await this.getCurrentWorkspace();
      const currentWorkspace = currentWorkspaceResult.success ? currentWorkspaceResult.data : null;
      const settingsResult = await StorageManager.getSettings();
      if (!settingsResult.success) return settingsResult;
      const settings = settingsResult.data;
      const switchOptions = {
        closeOtherTabs: options.closeOtherTabs ?? settings.autoCloseOtherTabs,
        preserveUserOpenedTabs: options.preserveUserOpenedTabs ?? settings.preserveUserOpenedTabs,
        focusFirstTab: options.focusFirstTab ?? true
      };
      if (currentWorkspace && currentWorkspace.id !== workspaceId) {
        await this.moveCurrentTabsToWorkspaceWindow(currentWorkspace);
      }
      await this.moveTabsFromWorkspaceWindow(workspace);
      await this.openWorkspaceWebsites(workspace);
      await StorageManager.setActiveWorkspaceId(workspaceId);
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success) {
        const workspaces = workspacesResult.data;
        workspaces.forEach((w) => {
          w.isActive = w.id === workspaceId;
        });
        await StorageManager.saveWorkspaces(workspaces);
      }
      if (switchOptions.focusFirstTab && workspace.websites.length > 0) {
        const firstWebsite = workspace.websites[0];
        const tabResult = await TabManager.findTabByUrl(firstWebsite.url);
        if (tabResult.success && tabResult.data) {
          await TabManager.activateTab(tabResult.data.id);
        }
      }
      console.log(`成功切换到工作区: ${workspace.name}`);
      return { success: true };
    } catch (error) {
      console.error(`切换工作区失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to switch workspace",
          details: error
        }
      };
    }
  }
  /**
   * 将当前窗口的工作区相关标签页移动到专用窗口
   */
  static async moveCurrentTabsToWorkspaceWindow(workspace) {
    try {
      console.log(`将工作区 ${workspace.name} 的标签页移动到专用窗口`);
      const workspaceTabsResult = await TabManager.getWorkspaceRelatedTabs(workspace);
      if (!workspaceTabsResult.success) {
        console.log(`获取工作区相关标签页失败:`, workspaceTabsResult.error);
        return { success: true };
      }
      const workspaceTabs = workspaceTabsResult.data;
      if (workspaceTabs.length === 0) {
        console.log(`工作区 ${workspace.name} 没有相关标签页需要移动`);
        return { success: true };
      }
      const tabIds = workspaceTabs.map((tab) => tab.id);
      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabIds,
        workspace.id,
        workspace.name
      );
      if (moveResult.success) {
        console.log(`成功移动 ${tabIds.length} 个标签页到工作区专用窗口`);
      } else {
        console.error(`移动标签页到专用窗口失败:`, moveResult.error);
      }
      return moveResult;
    } catch (error) {
      console.error(`移动当前标签页到工作区专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move current tabs to workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 从工作区专用窗口移动标签页到当前窗口
   */
  static async moveTabsFromWorkspaceWindow(workspace) {
    try {
      console.log(`从工作区 ${workspace.name} 的专用窗口移动标签页到主窗口`);
      const moveResult = await WindowManager.moveTabsFromWorkspaceWindow(workspace.id);
      if (moveResult.success) {
        const movedTabs = moveResult.data;
        console.log(`成功从专用窗口移动 ${movedTabs.length} 个标签页到主窗口`);
      } else {
        console.error(`从专用窗口移动标签页失败:`, moveResult.error);
      }
      return { success: true };
    } catch (error) {
      console.error(`从工作区专用窗口移动标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs from workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 打开工作区中尚未打开的网站
   */
  static async openWorkspaceWebsites(workspace) {
    try {
      console.log(`检查并打开工作区 ${workspace.name} 中缺失的网站`);
      for (const website of workspace.websites) {
        try {
          const existingTabResult = await TabManager.findTabByUrl(website.url);
          if (!existingTabResult.success || !existingTabResult.data) {
            console.log(`创建新标签页: ${website.title}`);
            const newTabResult = await TabManager.createTab(website.url, false, false);
            if (newTabResult.success) {
              console.log(`成功创建标签页: ${website.title}`);
            } else {
              console.error(`创建标签页失败 ${website.title}:`, newTabResult.error);
            }
          } else {
            console.log(`标签页已存在: ${website.title}`);
          }
        } catch (error) {
          console.error(`处理网站 ${website.title} 时出错:`, error);
        }
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to open workspace websites",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前活跃的工作区
   */
  static async getCurrentWorkspace() {
    try {
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (!activeIdResult.success) return activeIdResult;
      const activeId = activeIdResult.data;
      if (!activeId) {
        return { success: true, data: null };
      }
      const workspaceResult = await StorageManager.getWorkspace(activeId);
      if (!workspaceResult.success) {
        await StorageManager.setActiveWorkspaceId(null);
        return { success: true, data: null };
      }
      return { success: true, data: workspaceResult.data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get current workspace",
          details: error
        }
      };
    }
  }
  /**
   * 智能检测当前应该激活的工作区
   */
  static async detectActiveWorkspace() {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;
      const workspaces = workspacesResult.data;
      if (workspaces.length === 0) {
        return { success: true, data: null };
      }
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: true, data: null };
      }
      const activeTab = activeTabResult.data;
      const matchingWorkspace = workspaces.find(
        (workspace) => workspace.websites.some(
          (website) => activeTab.url.startsWith(website.url)
        )
      );
      return { success: true, data: matchingWorkspace || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to detect active workspace",
          details: error
        }
      };
    }
  }
  /**
   * 添加当前标签页到工作区
   */
  static async addCurrentTabToWorkspace(workspaceId) {
    try {
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) return activeTabResult;
      const activeTab = activeTabResult.data;
      const addResult = await WorkspaceManager.addWebsite(
        workspaceId,
        activeTab.url,
        {
          title: activeTab.title,
          favicon: activeTab.favicon,
          pinTab: activeTab.isPinned
        }
      );
      return addResult;
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to add current tab to workspace",
          details: error
        }
      };
    }
  }
  /**
   * 快速切换到下一个工作区
   */
  static async switchToNextWorkspace() {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;
      const workspaces = workspacesResult.data;
      if (workspaces.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: "No workspaces available"
          }
        };
      }
      const currentResult = await this.getCurrentWorkspace();
      if (!currentResult.success) return currentResult;
      const currentWorkspace = currentResult.data;
      let nextIndex = 0;
      if (currentWorkspace) {
        const currentIndex = workspaces.findIndex((w) => w.id === currentWorkspace.id);
        nextIndex = (currentIndex + 1) % workspaces.length;
      }
      const nextWorkspace = workspaces[nextIndex];
      return await this.switchToWorkspace(nextWorkspace.id);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to switch to next workspace",
          details: error
        }
      };
    }
  }
}

export { COMMANDS as C, StorageManager as S, URL_REGEX as U, WorkspaceManager as W, WorkspaceSwitcher as a, WORKSPACE_ICONS as b, WORKSPACE_COLORS as c, WORKSPACE_TEMPLATES as d };
